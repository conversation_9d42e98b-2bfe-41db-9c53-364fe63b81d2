#!/usr/bin/env python3
"""
Emergency Order Cancellation Script for MEXC Exchange
This script cancels all open orders immediately with rate limiting protection.
"""

import asyncio
import sys
import os
from decimal import Decimal
from typing import List

# Add hummingbot to path
sys.path.append('/root/hummingbot')

from hummingbot.client.config.config_helpers import read_system_configs_from_yml
from hummingbot.connector.exchange.mexc.mexc_exchange import MexcExchange
from hummingbot.core.utils.async_utils import safe_ensure_future
from hummingbot.client.config.client_config_map import ClientConfigMap
from hummingbot.client.config.config_helpers import ClientConfigAdapter


async def cancel_all_orders():
    """Cancel all open orders on MEXC exchange"""
    
    print("🚨 EMERGENCY ORDER CANCELLATION SCRIPT 🚨")
    print("=" * 50)
    
    try:
        # Load configuration
        print("📋 Loading configuration...")
        
        # Read the config file to get API credentials
        config_path = "/root/hummingbot/conf/conf_pure_mm_1.yml"
        if not os.path.exists(config_path):
            print(f"❌ Config file not found: {config_path}")
            return
        
        # Read MEXC credentials from environment or config
        mexc_api_key = os.getenv("MEXC_API_KEY")
        mexc_secret_key = os.getenv("MEXC_SECRET_KEY")
        
        if not mexc_api_key or not mexc_secret_key:
            print("❌ MEXC API credentials not found in environment variables")
            print("Please set MEXC_API_KEY and MEXC_SECRET_KEY environment variables")
            return
        
        print("✅ Configuration loaded successfully")
        
        # Initialize MEXC exchange
        print("🔗 Connecting to MEXC exchange...")
        
        # Create a minimal client config
        client_config_map = ClientConfigAdapter(ClientConfigMap())
        
        exchange = MexcExchange(
            client_config_map=client_config_map,
            mexc_api_key=mexc_api_key,
            mexc_secret_key=mexc_secret_key,
            trading_pairs=["FULA-USDT"],  # Add your trading pair
            trading_required=True
        )
        
        # Wait for exchange to be ready
        print("⏳ Waiting for exchange to connect...")
        max_wait = 30  # 30 seconds max wait
        wait_count = 0
        
        while not exchange.ready and wait_count < max_wait:
            await asyncio.sleep(1)
            wait_count += 1
            if wait_count % 5 == 0:
                print(f"   Still waiting... ({wait_count}/{max_wait}s)")
        
        if not exchange.ready:
            print("❌ Exchange failed to connect within 30 seconds")
            return
        
        print("✅ Connected to MEXC exchange")
        
        # Get all open orders
        print("📊 Fetching open orders...")
        
        open_orders = list(exchange.in_flight_orders.values())
        
        if not open_orders:
            print("✅ No open orders found - nothing to cancel")
            return
        
        print(f"🎯 Found {len(open_orders)} open orders to cancel:")
        for order in open_orders:
            side = "BUY" if order.is_buy else "SELL"
            print(f"   - {order.client_order_id}: {side} {order.amount} {order.trading_pair} @ {order.price}")
        
        # Cancel all orders using rate-limited method
        print("\n🚨 CANCELING ALL ORDERS WITH RATE LIMITING 🚨")
        print("=" * 50)
        
        order_ids = [order.client_order_id for order in open_orders]
        
        # Use the rate-limited cancellation method
        if hasattr(exchange, 'cancel_orders_rate_limited'):
            print("📤 Using rate-limited batch cancellation...")
            await exchange.cancel_orders_rate_limited(order_ids)
        else:
            print("📤 Using individual order cancellation...")
            # Fallback to individual cancellation with manual rate limiting
            for i, order_id in enumerate(order_ids):
                try:
                    print(f"   Canceling order {i+1}/{len(order_ids)}: {order_id}")
                    tracked_order = exchange._order_tracker.all_updatable_orders.get(order_id)
                    if tracked_order:
                        await exchange._place_cancel(order_id, tracked_order)
                    
                    # Rate limiting: max 10 orders per second
                    if (i + 1) % 10 == 0 and i + 1 < len(order_ids):
                        print("   ⏳ Rate limiting: waiting 1 second...")
                        await asyncio.sleep(1)
                        
                except Exception as e:
                    print(f"   ❌ Failed to cancel {order_id}: {e}")
        
        # Wait for cancellations to complete
        print("\n⏳ Waiting for cancellations to complete...")
        await asyncio.sleep(5)
        
        # Check remaining orders
        remaining_orders = list(exchange.in_flight_orders.values())
        
        if remaining_orders:
            print(f"⚠️  {len(remaining_orders)} orders still active:")
            for order in remaining_orders:
                side = "BUY" if order.is_buy else "SELL"
                print(f"   - {order.client_order_id}: {side} {order.amount} {order.trading_pair}")
        else:
            print("✅ ALL ORDERS SUCCESSFULLY CANCELED!")
        
        print("\n🎯 CANCELLATION COMPLETE!")
        print("=" * 50)
        
    except Exception as e:
        print(f"❌ ERROR: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        # Clean shutdown
        try:
            if 'exchange' in locals():
                await exchange.stop()
        except:
            pass


def main():
    """Main entry point"""
    print("Starting emergency order cancellation...")
    
    # Check if we're in the right directory
    if not os.path.exists("/root/hummingbot"):
        print("❌ Please run this script from the hummingbot directory")
        return
    
    # Run the async function
    asyncio.run(cancel_all_orders())


if __name__ == "__main__":
    main()
